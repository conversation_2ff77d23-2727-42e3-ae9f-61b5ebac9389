/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e7eb;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: #4f46e5;
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #4f46e5;
}

.nav-cta {
    margin-left: 1rem;
}

/* Buttons */
.cta-button {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 90vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #1e293b 0%, #4f46e5 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-cta {
    font-size: 1.1rem;
    padding: 16px 32px;
}

/* Dashboard Mockup */
.dashboard-mockup {
    background: white;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.dashboard-mockup:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.mockup-header {
    background: #f1f5f9;
    padding: 12px 16px;
    border-bottom: 1px solid #e2e8f0;
}

.mockup-dots {
    display: flex;
    gap: 8px;
}

.mockup-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #94a3b8;
}

.mockup-dots span:first-child {
    background: #ef4444;
}

.mockup-dots span:nth-child(2) {
    background: #f59e0b;
}

.mockup-dots span:last-child {
    background: #10b981;
}

.mockup-content {
    padding: 24px;
}

.chart-container {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 120px;
    margin-bottom: 24px;
    gap: 8px;
}

.chart-bar {
    background: linear-gradient(180deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 4px 4px 0 0;
    flex: 1;
    animation: growUp 0.8s ease-out;
}

@keyframes growUp {
    from { height: 0 !important; }
    to { height: var(--final-height); }
}

.insights-panel {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 8px;
}

.insight-icon {
    font-size: 1.2rem;
}

.insight-text {
    color: #475569;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1e293b;
}

.section-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.section-text h2 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1e293b;
}

.section-text p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 1rem;
    line-height: 1.7;
}

/* Dual Purpose Section */
.dual-purpose {
    background: white;
}

.chat-interfaces {
    display: flex;
    gap: 1.5rem;
    align-items: start;
}

.chat-window {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    flex: 1;
}

.chat-header {
    background: #4f46e5;
    color: white;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
}

.chat-messages {
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.message {
    padding: 8px 12px;
    border-radius: 12px;
    max-width: 80%;
}

.message.bot {
    background: #f1f5f9;
    color: #475569;
    align-self: flex-start;
}

.message.user {
    background: #4f46e5;
    color: white;
    align-self: flex-end;
}

.insights-feed {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 16px;
    flex: 1;
}

.insights-feed h4 {
    color: #1e293b;
    margin-bottom: 16px;
    font-weight: 600;
}

.feedback-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.feedback-item:last-child {
    border-bottom: none;
}

.feedback-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #e2e8f0;
}

.feedback-text {
    font-size: 0.9rem;
    color: #64748b;
}

/* Process Section */
.process {
    background: #f8fafc;
}

.process-steps {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
}

.process-step {
    text-align: center;
    flex: 1;
    max-width: 300px;
}

.step-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.process-step h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.process-step p {
    color: #64748b;
    line-height: 1.6;
}

.process-arrow {
    font-size: 2rem;
    color: #4f46e5;
    font-weight: bold;
}

/* Market Validation Section */
.market-validation {
    background: white;
}

.validation-dashboard {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 24px;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.metric-card {
    background: #f8fafc;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.metric-card h4 {
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-number {
    font-size: 2rem;
    font-weight: 700;
    color: #4f46e5;
}

/* Discovery Phase Section */
.discovery-phase {
    background: #f8fafc;
}

.discovery-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Strategic Direction Section */
.strategic-direction {
    background: white;
}

.data-visualization {
    position: relative;
    height: 300px;
    background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    border-radius: 16px;
    overflow: hidden;
}

.data-cloud {
    position: relative;
    width: 100%;
    height: 100%;
}

.data-point {
    position: absolute;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    animation: float 3s ease-in-out infinite;
}

.data-point:nth-child(odd) {
    animation-delay: -1.5s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Implementation Section */
.implementation {
    background: #f8fafc;
}

.implementation-steps {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.impl-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1.5rem;
}

.impl-step h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1e293b;
}

.impl-step p {
    color: #64748b;
    line-height: 1.6;
}

/* Final CTA Section */
.final-cta {
    background: linear-gradient(135deg, #1e293b 0%, #4f46e5 100%);
    color: white;
    text-align: center;
}

.cta-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 3rem;
}

.cta-text h2 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-text p {
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.7;
}

.team-illustration {
    position: relative;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.team-member {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    position: absolute;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.team-member:nth-child(1) {
    left: 20%;
    top: 30%;
}

.team-member:nth-child(2) {
    right: 20%;
    top: 20%;
}

.team-member:nth-child(3) {
    left: 50%;
    transform: translateX(-50%);
    top: 50%;
}

.laptop-screen {
    width: 120px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
}

.screen-content {
    width: 90%;
    height: 70%;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    margin: 8px auto;
}

.final-cta-btn {
    background: white;
    color: #4f46e5;
    font-size: 1.1rem;
    padding: 16px 32px;
}

.final-cta-btn:hover {
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

/* Footer */
.footer {
    background: #1e293b;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.footer-brand h3 {
    color: #4f46e5;
    margin-bottom: 8px;
}

.footer-brand p {
    opacity: 0.7;
    font-size: 0.9rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: white;
    text-decoration: none;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 1;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #374151;
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .hero-content,
    .section-content,
    .cta-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .process-steps {
        flex-direction: column;
        gap: 3rem;
    }
    
    .process-arrow {
        transform: rotate(90deg);
    }
    
    .discovery-features,
    .implementation-steps {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .chat-interfaces {
        flex-direction: column;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .section-text h2 {
        font-size: 1.75rem;
    }
}