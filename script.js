// Smooth scrolling for navigation links
document.addEventListener('DOMContentLoaded', function() {
    // Handle smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Handle CTA button clicks (placeholder for actual booking functionality)
    const ctaButtons = document.querySelectorAll('.cta-button');
    
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Placeholder for actual booking/contact functionality
            // This could integrate with a calendar booking system, contact form, etc.
            alert('Thank you for your interest! This would normally open a booking calendar or contact form.');
        });
    });

    // Navbar background opacity on scroll
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        }
    });

    // Animate elements on scroll (Intersection Observer)
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.process-step, .feature-card, .impl-step, .metric-card');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Animate chart bars when they come into view
    const chartBars = document.querySelectorAll('.chart-bar');
    const chartObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const bars = entry.target.querySelectorAll('.chart-bar');
                bars.forEach((bar, index) => {
                    setTimeout(() => {
                        bar.style.animation = 'none';
                        bar.offsetHeight; // Trigger reflow
                        bar.style.animation = 'growUp 0.8s ease-out';
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    const chartContainer = document.querySelector('.chart-container');
    if (chartContainer) {
        chartObserver.observe(chartContainer);
    }

    // Add hover effects for interactive elements
    const interactiveCards = document.querySelectorAll('.feature-card, .metric-card, .dashboard-mockup');
    
    interactiveCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = this.style.transform.includes('scale') ? 
                this.style.transform : this.style.transform + ' scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = this.style.transform.replace(' scale(1.02)', '');
        });
    });

    // Floating animation for data points
    const dataPoints = document.querySelectorAll('.data-point');
    dataPoints.forEach((point, index) => {
        point.style.animationDelay = `${index * 0.5}s`;
    });

    // Mobile menu toggle (if you want to add mobile menu later)
    const createMobileMenu = () => {
        const navContainer = document.querySelector('.nav-container');
        const navMenu = document.querySelector('.nav-menu');
        
        // Create mobile menu button
        const mobileMenuBtn = document.createElement('button');
        mobileMenuBtn.className = 'mobile-menu-btn';
        mobileMenuBtn.innerHTML = '☰';
        mobileMenuBtn.style.display = 'none';
        mobileMenuBtn.style.background = 'none';
        mobileMenuBtn.style.border = 'none';
        mobileMenuBtn.style.fontSize = '1.5rem';
        mobileMenuBtn.style.color = '#4f46e5';
        mobileMenuBtn.style.cursor = 'pointer';
        
        navContainer.appendChild(mobileMenuBtn);
        
        // Toggle mobile menu
        let isMenuOpen = false;
        mobileMenuBtn.addEventListener('click', () => {
            isMenuOpen = !isMenuOpen;
            navMenu.style.display = isMenuOpen ? 'flex' : 'none';
            navMenu.style.position = isMenuOpen ? 'absolute' : 'relative';
            navMenu.style.top = isMenuOpen ? '100%' : 'auto';
            navMenu.style.left = isMenuOpen ? '0' : 'auto';
            navMenu.style.right = isMenuOpen ? '0' : 'auto';
            navMenu.style.background = isMenuOpen ? 'white' : 'transparent';
            navMenu.style.flexDirection = isMenuOpen ? 'column' : 'row';
            navMenu.style.padding = isMenuOpen ? '1rem' : '0';
            navMenu.style.boxShadow = isMenuOpen ? '0 4px 6px rgba(0,0,0,0.1)' : 'none';
        });
        
        // Show/hide mobile menu button based on screen size
        const checkScreenSize = () => {
            if (window.innerWidth <= 768) {
                mobileMenuBtn.style.display = 'block';
                navMenu.style.display = isMenuOpen ? 'flex' : 'none';
            } else {
                mobileMenuBtn.style.display = 'none';
                navMenu.style.display = 'flex';
                navMenu.style.position = 'relative';
                navMenu.style.flexDirection = 'row';
                navMenu.style.background = 'transparent';
                navMenu.style.padding = '0';
                navMenu.style.boxShadow = 'none';
                isMenuOpen = false;
            }
        };
        
        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
    };
    
    // Initialize mobile menu
    createMobileMenu();

    // Add loading animation completion
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');
    });
});